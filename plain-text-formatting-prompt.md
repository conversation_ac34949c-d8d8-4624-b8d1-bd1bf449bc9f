# Update Response Formatting to Plain Text

## Goal
Convert all Telegram bot responses from Markdown formatting to plain text to ensure consistent, reliable message delivery without formatting conflicts or parsing errors.

## Current State Analysis
Your bot currently has mixed formatting:
- **Markdown formatting**: Used in task list displays with `parse_mode: 'Markdown'`
- **Plain text**: Used in most regular responses from Agent Hustle API
- **Escape functions**: `escapeMarkdown()` function exists but creates complexity

## Implementation Tasks

### Task 1: Remove Markdown Parse Mode
**File**: `app.js`
**Location**: Line 499 (task list display)

**Current Code**:
```javascript
bot.sendMessage(chatId, taskListMessage, { parse_mode: 'Markdown' }).catch(error => {
  secureLog('error', `Error sending task list: ${error.message}. Raw message: ${taskListMessage}`);
  // Fallback to sending without Markdown if parsing fails
  bot.sendMessage(chatId, "Could not display task list with formatting. Sending raw list:\n\n" + taskListMessage.replace(/[_*[\]()~`>#+\-=|{}.!]/g, '')); // Basic strip for safety
});
```

**Updated Code**:
```javascript
// Send task list as plain text - no parse_mode needed
bot.sendMessage(chatId, taskListMessage).catch(error => {
  secureLog('error', `Error sending task list: ${error.message}`);
  // Simplified fallback since we're already using plain text
  bot.sendMessage(chatId, "Could not display task list. Please try again.");
});
```

### Task 2: Update Task List Formatting
**File**: `app.js` 
**Location**: Around lines 450-495 (task list generation)

**Pattern**: Replace Markdown syntax with plain text equivalents:
- Remove `*bold*` → use `UPPERCASE` or simple text
- Remove `_italic_` → use simple text
- Remove `` `code` `` → use quotes or simple text
- Replace bullet points with simple dashes or numbers

**Example Transformation**:
```javascript
// Before (Markdown)
const taskInfo = `*${taskName}*\n` +
                `Schedule: \`${task.schedule}\`\n` +
                `Status: ${task.running ? '✅ *Running*' : '⏸️ *Paused*'}\n`;

// After (Plain Text)
const taskInfo = `${taskName.toUpperCase()}\n` +
                `Schedule: ${task.schedule}\n` +
                `Status: ${task.running ? '✅ RUNNING' : '⏸️ PAUSED'}\n`;
```

### Task 3: Remove Markdown Escape Function
**File**: `app.js`
**Location**: Lines 79-87

**Action**: 
1. Remove the `escapeMarkdown()` function entirely
2. Remove any calls to `escapeMarkdown()` throughout the codebase
3. Ensure all text is sent as-is without escaping

### Task 4: Update All bot.sendMessage Calls
**File**: `app.js`
**Locations**: Multiple locations (lines 249, 277, 288, 312, 347, etc.)

**Pattern**: Ensure NO `parse_mode` parameter is used:
```javascript
// Correct - Plain text (default)
await bot.sendMessage(chatId, messageText);

// Remove any instances of:
await bot.sendMessage(chatId, messageText, { parse_mode: 'Markdown' });
await bot.sendMessage(chatId, messageText, { parse_mode: 'HTML' });
```

### Task 5: Update Response Processing
**File**: `app.js`
**Location**: Lines 309-312 (regular responses) and streaming responses

**Current Pattern**:
```javascript
let messageToSend = typeof response === 'string' ? response : response.content || 'No response content';
messageToSend = safeTruncateMessage(messageToSend);
await bot.sendMessage(chatId, messageToSend);
```

**Keep As-Is**: This is already plain text, no changes needed.

## Plain Text Formatting Guidelines

### Use Simple Text Enhancements:
- **Headers**: Use UPPERCASE or add separators like "==="
- **Emphasis**: Use UPPERCASE or repeat characters like "!!!"
- **Lists**: Use simple dashes "-" or numbers "1."
- **Code/Commands**: Use quotes or simple brackets
- **Status**: Use emojis + plain text

### Examples:
```
TASK MANAGEMENT
===============

1. Task Name: DAILY_REPORT
   Schedule: 0 9 * * *
   Status: ✅ RUNNING
   Target: General Chat

2. Task Name: WEEKLY_SUMMARY  
   Schedule: 0 9 * * 1
   Status: ⏸️ PAUSED
   Target: Admin Chat

Commands:
- /addtask - Create new automated task
- /listtasks - Show all tasks
- /edittask - Modify existing task
```

## Benefits of Plain Text
1. **Reliability**: No parsing errors or formatting conflicts
2. **Simplicity**: Easier to maintain and debug
3. **Compatibility**: Works with all Telegram clients
4. **Performance**: Faster message processing
5. **Consistency**: All responses use same format

## Testing Checklist
After implementation:
- [ ] All messages send without formatting errors
- [ ] Task lists display correctly in plain text
- [ ] No `parse_mode` parameters remain in code
- [ ] `escapeMarkdown()` function removed
- [ ] Streaming responses work without formatting issues
- [ ] Error messages are clear and readable
- [ ] Emojis and special characters display correctly

## Files to Modify
- `app.js` - Main bot file with all formatting logic

## Validation Commands
Test these commands after changes:
- `/start` - Welcome message
- `/help` - Help text
- `/hustle Hello` - Regular response
- `/hustlestream Tell me a story` - Streaming response
- `/listtasks` - Task list display (if you have automated tasks)

This change will make your bot more reliable and easier to maintain while ensuring all users receive properly formatted messages regardless of their Telegram client.
